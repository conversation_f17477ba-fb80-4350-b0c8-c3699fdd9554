import { z } from 'zod';

import { EmployeeStatus, EmploymentType, MaritalStatus } from '../types/employee.types';

/**
 * Schema validation cho tạo nhân viên mới
 */
export const createEmployeeSchema = z.object({
  userId: z.number({
    required_error: 'ID người dùng là bắt buộc',
    invalid_type_error: 'ID người dùng phải là số',
  }),
  employeeCode: z
    .string({
      required_error: 'Mã nhân viên là bắt buộc',
    })
    .min(2, 'Mã nhân viên phải có ít nhất 2 ký tự')
    .max(50, 'Mã nhân viên không được vượt quá 50 ký tự'),
  departmentId: z.number().nullable().optional(),
  jobTitle: z.string().max(255, 'Chức danh không được vượt quá 255 ký tự').nullable().optional(),
  jobLevel: z.string().max(50, '<PERSON><PERSON><PERSON> b<PERSON><PERSON> không được vượt quá 50 ký tự').nullable().optional(),
  managerId: z.number().nullable().optional(),
  employmentType: z.nativeEnum(EmploymentType).nullable().optional(),
  status: z.nativeEnum(EmployeeStatus).default(EmployeeStatus.ACTIVE),
  hireDate: z.union([z.string(), z.date()]).nullable().optional(),
  probationEndDate: z.union([z.string(), z.date()]).nullable().optional(),
  dateOfBirth: z.union([z.string(), z.date()]).nullable().optional(),
  gender: z.string().nullable().optional(),
  address: z.string().max(255, 'Địa chỉ không được vượt quá 255 ký tự').nullable().optional(),
  city: z.string().max(100, 'Thành phố không được vượt quá 100 ký tự').nullable().optional(),
  state: z.string().max(100, 'Tỉnh/Bang không được vượt quá 100 ký tự').nullable().optional(),
  country: z.string().max(100, 'Quốc gia không được vượt quá 100 ký tự').nullable().optional(),
  postalCode: z.string().max(20, 'Mã bưu điện không được vượt quá 20 ký tự').nullable().optional(),
  maritalStatus: z.nativeEnum(MaritalStatus).nullable().optional(),
  numberOfDependents: z.number().nullable().optional(),
  emergencyContactName: z
    .string()
    .max(255, 'Tên liên hệ khẩn cấp không được vượt quá 255 ký tự')
    .nullable()
    .optional(),
  emergencyContactPhone: z
    .string()
    .max(20, 'Số điện thoại liên hệ khẩn cấp không được vượt quá 20 ký tự')
    .nullable()
    .optional(),
  emergencyContactRelationship: z
    .string()
    .max(100, 'Mối quan hệ liên hệ khẩn cấp không được vượt quá 100 ký tự')
    .nullable()
    .optional(),
  notes: z.string().nullable().optional(),
});

/**
 * Schema validation cho cập nhật nhân viên
 */
export const updateEmployeeSchema = createEmployeeSchema.partial().omit({ userId: true });

/**
 * Schema validation cho phân quyền nhân viên
 */
export const assignRoleSchema = z.object({
  roleIds: z
    .array(z.number(), {
      required_error: 'Danh sách vai trò là bắt buộc',
      invalid_type_error: 'Danh sách vai trò phải là mảng các số',
    })
    .min(1, 'Phải chọn ít nhất một vai trò'),
});
