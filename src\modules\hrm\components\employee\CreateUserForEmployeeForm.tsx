import React from 'react';
import { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { Button, Card, Form, FormGrid, FormItem, Icon, Input } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { useFormErrors } from '@/shared/hooks';

import { useCreateUserForEmployee } from '../../hooks/useEmployeeUser';
import { loadEmployeesForAsyncSelect } from '../../hooks/useEmployees';
import { createUserForEmployeeSchema } from '../../schemas/employee-user.schema';

// Type cho form values
type CreateUserFormValues = z.infer<ReturnType<typeof createUserForEmployeeSchema>>;

// Props cho component
interface CreateUserForEmployeeFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  employeeId?: number;
}

/**
 * Form tạo tài khoản người dùng cho nhân viên
 */
const CreateUserForEmployeeForm: React.FC<CreateUserForEmployeeFormProps> = ({
  onSuccess,
  onCancel,
  employeeId,
}) => {
  const { t } = useTranslation(['hrm', 'common']);
  const { formRef, setFormErrors } = useFormErrors<CreateUserFormValues>();
  const createUserMutation = useCreateUserForEmployee();
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);
  const [generalError, setGeneralError] = React.useState<string>('');

  // Schema cho form
  const schema = createUserForEmployeeSchema(t);

  // Xử lý submit form
  const handleSubmit = (values: unknown) => {
    try {
      // Reset form errors and general error
      setFormErrors({});
      setGeneralError('');

      // Type assertion to the correct type
      const formValues = values as CreateUserFormValues;

      // Prepare data for API
      const submitData = {
        username: formValues.username,
        password: formValues.password,
        email: formValues.email,
        fullName: formValues.fullName,
        employeeId: employeeId || formValues.employeeId,
      };

      // Call API to create user
      createUserMutation.mutate(submitData, {
        onSuccess: () => {
          if (onSuccess) {
            onSuccess();
          }
        },
        onError: (error: any) => {
          // Handle API errors
          if (error?.response?.data?.message) {
            // Show general error
            setGeneralError(error.response.data.message);
          }

          // Handle field-specific errors
          if (error?.response?.data?.errors) {
            setFormErrors(error.response.data.errors);
          }
        },
      });
    } catch (error) {
      console.error('Error submitting form:', error);
      setGeneralError(t('common:error.unexpected', 'Đã xảy ra lỗi không mong muốn'));
    }
  };

  return (
    <Card>
      <Form
        ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>}
        schema={schema}
        onSubmit={handleSubmit}
        className="space-y-6"
      >
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-6">
            {t('hrm:employee.form.createUserTitle', 'Tạo tài khoản người dùng cho nhân viên')}
          </h2>

          {/* Display general error if any */}
          {generalError && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md">
              <p className="text-red-700 dark:text-red-300 text-sm">{generalError}</p>
            </div>
          )}

          <div className="space-y-6">
            <FormItem
              name="username"
              label={t('hrm:employee.form.username', 'Tên đăng nhập')}
              required
            >
              <Input fullWidth />
            </FormItem>

            <FormGrid columns={2}>
              <FormItem
                name="password"
                label={t('hrm:employee.form.password', 'Mật khẩu')}
                required
              >
                <Input
                  type={showPassword ? 'text' : 'password'}
                  rightIcon={
                    <div className="cursor-pointer" onClick={() => setShowPassword(!showPassword)}>
                      <Icon name={showPassword ? 'eye-off' : 'eye'} size="sm" />
                    </div>
                  }
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="confirmPassword"
                label={t('hrm:employee.form.confirmPassword', 'Xác nhận mật khẩu')}
                required
              >
                <Input
                  type={showConfirmPassword ? 'text' : 'password'}
                  rightIcon={
                    <div
                      className="cursor-pointer"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      <Icon name={showConfirmPassword ? 'eye-off' : 'eye'} size="sm" />
                    </div>
                  }
                  fullWidth
                />
              </FormItem>
            </FormGrid>

            <FormItem name="email" label={t('hrm:employee.form.email', 'Email')} required>
              <Input type="email" fullWidth />
            </FormItem>

            <FormItem
              name="fullName"
              label={t('hrm:employee.form.fullName', 'Họ và tên đầy đủ')}
              required
            >
              <Input fullWidth />
            </FormItem>

            <FormItem name="employeeId" label={t('hrm:employee.form.employee', 'Nhân viên')}>
              <AsyncSelectWithPagination
                loadOptions={loadEmployeesForAsyncSelect}
                onChange={(value: string | string[] | number | number[] | undefined) => {
                  if (formRef.current && value) {
                    formRef.current.setValues({ employeeId: Number(value) });
                  }
                }}
                placeholder={t('hrm:employee.form.employeePlaceholder', 'Tìm kiếm nhân viên...')}
                debounceTime={300}
                noOptionsMessage={t('common:noResults', 'Không tìm thấy kết quả')}
                loadingMessage={t('common:loading', 'Đang tìm kiếm...')}
                autoLoadInitial={true}
                fullWidth
              />
            </FormItem>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            {onCancel && (
              <Button variant="outline" onClick={onCancel} disabled={createUserMutation.isPending}>
                {t('common:cancel', 'Hủy')}
              </Button>
            )}
            <Button
              type="submit"
              isLoading={createUserMutation.isPending}
              disabled={createUserMutation.isPending}
            >
              {t('common:save', 'Lưu')}
            </Button>
          </div>
        </div>
      </Form>
    </Card>
  );
};

export default CreateUserForEmployeeForm;
